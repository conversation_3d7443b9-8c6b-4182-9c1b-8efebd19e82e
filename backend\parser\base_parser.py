"""
Base parser class for document text extraction.
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional


class BaseParser(ABC):
    """Abstract base class for document parsers."""
    
    def __init__(self):
        self.supported_extensions: set[str] = set()
    
    @abstractmethod
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from document.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Extracted text as string
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: If parsing fails
        """
        pass
    
    def is_supported(self, file_path: Path) -> bool:
        """
        Check if file format is supported by this parser.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if format is supported, False otherwise
        """
        return file_path.suffix.lower() in self.supported_extensions
    
    def validate_file(self, file_path: Path) -> None:
        """
        Validate file before parsing.
        
        Args:
            file_path: Path to the file
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If file format is not supported
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if not self.is_supported(file_path):
            raise ValueError(
                f"Unsupported file format: {file_path.suffix}. "
                f"Supported formats: {', '.join(self.supported_extensions)}"
            )
    
    def clean_text(self, text: str) -> str:
        """
        Clean extracted text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = [line.strip() for line in text.split('\n')]
        lines = [line for line in lines if line]
        
        return '\n'.join(lines)
