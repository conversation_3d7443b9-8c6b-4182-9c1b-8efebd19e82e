"""
PDF document parser using PyMuPDF.
"""

from pathlib import Path
from typing import Optional
import fitz  # PyMuPDF
from loguru import logger

from .base_parser import BaseParser


class PDFParser(BaseParser):
    """Parser for PDF documents using PyMuPDF."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.pdf'}
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from PDF file.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Extracted text as string
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: If PDF parsing fails
        """
        self.validate_file(file_path)
        
        try:
            logger.info(f"Extracting text from PDF: {file_path}")
            
            doc = fitz.open(str(file_path))
            text_parts = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                if text.strip():
                    text_parts.append(text)
                    logger.debug(f"Extracted {len(text)} characters from page {page_num + 1}")
            
            doc.close()
            
            if not text_parts:
                logger.warning(f"No text found in PDF: {file_path}")
                return ""
            
            full_text = '\n'.join(text_parts)
            cleaned_text = self.clean_text(full_text)
            
            logger.info(f"Successfully extracted {len(cleaned_text)} characters from PDF")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Failed to extract text from PDF {file_path}: {e}")
            raise Exception(f"PDF parsing failed: {e}") from e
    
    def get_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from PDF file.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Dictionary with metadata
        """
        try:
            doc = fitz.open(str(file_path))
            metadata = doc.metadata
            doc.close()
            
            return {
                'title': metadata.get('title', ''),
                'author': metadata.get('author', ''),
                'subject': metadata.get('subject', ''),
                'creator': metadata.get('creator', ''),
                'producer': metadata.get('producer', ''),
                'creation_date': metadata.get('creationDate', ''),
                'modification_date': metadata.get('modDate', ''),
                'pages': len(doc)
            }
        except Exception as e:
            logger.error(f"Failed to extract metadata from PDF {file_path}: {e}")
            return {}
