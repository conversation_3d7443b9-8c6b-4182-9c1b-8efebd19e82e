# TTS-WEB Configuration

# Server settings
HOST=127.0.0.1
PORT=8000
DEBUG=true

# File upload settings
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=pdf,docx,txt,epub,rtf

# TTS settings
TTS_MODEL=tts_models/ru/ruslan/tacotron2-DDC
TTS_LANGUAGE=ru
TTS_SPEAKER_WAV=
USE_GPU=false

# Audio settings
AUDIO_FORMAT=wav
AUDIO_SAMPLE_RATE=22050
AUDIO_BITRATE=128

# Storage settings
UPLOAD_DIR=uploads
OUTPUT_DIR=output
TEMP_DIR=temp

# Cleanup settings
AUTO_CLEANUP_HOURS=24
MAX_FILES_PER_USER=10

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
