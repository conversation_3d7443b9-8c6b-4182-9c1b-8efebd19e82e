"""
Document parser module for TTS-WEB application.
Provides text extraction from various document formats.
"""

from .base_parser import BaseParser
from .pdf_parser import PDFParser
from .docx_parser import DOCXParser
from .epub_parser import EPUBParser
from .txt_parser import TXTParser
from .document_parser import DocumentParser

__all__ = [
    'BaseParser',
    'PDFParser', 
    'DOCXParser',
    'EPUBParser',
    'TXTParser',
    'DocumentParser'
]
