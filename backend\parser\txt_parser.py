"""
TXT document parser with encoding detection.
"""

from pathlib import Path
from typing import Optional
import chardet
from loguru import logger

from .base_parser import BaseParser


class TXTParser(BaseParser):
    """Parser for TXT documents with automatic encoding detection."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.txt', '.rtf', '.md', '.rst'}
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from TXT file with encoding detection.
        
        Args:
            file_path: Path to the TXT file
            
        Returns:
            Extracted text as string
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: If TXT parsing fails
        """
        self.validate_file(file_path)
        
        try:
            logger.info(f"Extracting text from TXT: {file_path}")
            
            # Detect encoding
            encoding = self._detect_encoding(file_path)
            logger.debug(f"Detected encoding: {encoding}")
            
            # Read file with detected encoding
            with open(file_path, 'r', encoding=encoding) as file:
                text = file.read()
            
            if not text.strip():
                logger.warning(f"No text found in TXT: {file_path}")
                return ""
            
            cleaned_text = self.clean_text(text)
            
            logger.info(f"Successfully extracted {len(cleaned_text)} characters from TXT")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Failed to extract text from TXT {file_path}: {e}")
            raise Exception(f"TXT parsing failed: {e}") from e
    
    def _detect_encoding(self, file_path: Path) -> str:
        """
        Detect file encoding using chardet.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Detected encoding name
        """
        try:
            with open(file_path, 'rb') as file:
                raw_data = file.read()
            
            result = chardet.detect(raw_data)
            encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)
            
            logger.debug(f"Encoding detection: {encoding} (confidence: {confidence:.2f})")
            
            # Fallback to utf-8 if confidence is too low
            if confidence < 0.7:
                logger.warning(f"Low confidence in encoding detection, using utf-8")
                encoding = 'utf-8'
            
            return encoding
            
        except Exception as e:
            logger.warning(f"Encoding detection failed, using utf-8: {e}")
            return 'utf-8'
    
    def get_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from TXT file.
        
        Args:
            file_path: Path to the TXT file
            
        Returns:
            Dictionary with metadata
        """
        try:
            stat = file_path.stat()
            encoding = self._detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read()
            
            lines = content.split('\n')
            words = content.split()
            
            return {
                'encoding': encoding,
                'size_bytes': stat.st_size,
                'lines_count': len(lines),
                'words_count': len(words),
                'characters_count': len(content),
                'created': stat.st_ctime,
                'modified': stat.st_mtime
            }
        except Exception as e:
            logger.error(f"Failed to extract metadata from TXT {file_path}: {e}")
            return {}
