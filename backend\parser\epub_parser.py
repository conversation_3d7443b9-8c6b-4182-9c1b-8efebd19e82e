"""
EPUB document parser using ebooklib.
"""

from pathlib import Path
from typing import Optional
import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
from loguru import logger

from .base_parser import BaseParser


class EPUBParser(BaseParser):
    """Parser for EPUB documents using ebooklib."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.epub'}
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from EPUB file.
        
        Args:
            file_path: Path to the EPUB file
            
        Returns:
            Extracted text as string
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: If EPUB parsing fails
        """
        self.validate_file(file_path)
        
        try:
            logger.info(f"Extracting text from EPUB: {file_path}")
            
            book = epub.read_epub(str(file_path))
            text_parts = []
            
            # Extract text from all items
            for item in book.get_items():
                if item.get_type() == ebooklib.ITEM_DOCUMENT:
                    content = item.get_content().decode('utf-8')
                    
                    # Parse HTML content
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    # Extract text
                    text = soup.get_text()
                    
                    if text.strip():
                        text_parts.append(text)
                        logger.debug(f"Extracted {len(text)} characters from item: {item.get_name()}")
            
            if not text_parts:
                logger.warning(f"No text found in EPUB: {file_path}")
                return ""
            
            full_text = '\n'.join(text_parts)
            cleaned_text = self.clean_text(full_text)
            
            logger.info(f"Successfully extracted {len(cleaned_text)} characters from EPUB")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Failed to extract text from EPUB {file_path}: {e}")
            raise Exception(f"EPUB parsing failed: {e}") from e
    
    def get_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from EPUB file.
        
        Args:
            file_path: Path to the EPUB file
            
        Returns:
            Dictionary with metadata
        """
        try:
            book = epub.read_epub(str(file_path))
            
            return {
                'title': book.get_metadata('DC', 'title')[0][0] if book.get_metadata('DC', 'title') else '',
                'author': book.get_metadata('DC', 'creator')[0][0] if book.get_metadata('DC', 'creator') else '',
                'language': book.get_metadata('DC', 'language')[0][0] if book.get_metadata('DC', 'language') else '',
                'publisher': book.get_metadata('DC', 'publisher')[0][0] if book.get_metadata('DC', 'publisher') else '',
                'date': book.get_metadata('DC', 'date')[0][0] if book.get_metadata('DC', 'date') else '',
                'description': book.get_metadata('DC', 'description')[0][0] if book.get_metadata('DC', 'description') else '',
                'identifier': book.get_metadata('DC', 'identifier')[0][0] if book.get_metadata('DC', 'identifier') else '',
                'items_count': len(list(book.get_items()))
            }
        except Exception as e:
            logger.error(f"Failed to extract metadata from EPUB {file_path}: {e}")
            return {}
