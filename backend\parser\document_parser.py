"""
Main document parser that routes to specific parsers based on file type.
"""

from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger

from .pdf_parser import PDFParser
from .docx_parser import DOCXParser
from .epub_parser import EPUBParser
from .txt_parser import TXTParser


class DocumentParser:
    """Main document parser that routes to specific parsers."""
    
    def __init__(self):
        self.parsers = {
            '.pdf': PDFParser(),
            '.docx': DOCXParser(),
            '.docm': DOCXParser(),
            '.epub': EPUBParser(),
            '.txt': TXTParser(),
            '.rtf': TXTParser(),
            '.md': TXTParser(),
            '.rst': TXTParser()
        }
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from document using appropriate parser.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Extracted text as string
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: If parsing fails
        """
        if not isinstance(file_path, Path):
            file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        extension = file_path.suffix.lower()
        
        if extension not in self.parsers:
            supported_formats = ', '.join(self.parsers.keys())
            raise ValueError(
                f"Unsupported file format: {extension}. "
                f"Supported formats: {supported_formats}"
            )
        
        parser = self.parsers[extension]
        
        try:
            logger.info(f"Parsing document: {file_path} using {parser.__class__.__name__}")
            text = parser.extract_text(file_path)
            
            if not text.strip():
                logger.warning(f"No text extracted from: {file_path}")
                return ""
            
            logger.info(f"Successfully extracted {len(text)} characters from {file_path}")
            return text
            
        except Exception as e:
            logger.error(f"Failed to parse document {file_path}: {e}")
            raise
    
    def get_metadata(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract metadata from document using appropriate parser.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary with metadata
        """
        if not isinstance(file_path, Path):
            file_path = Path(file_path)
        
        if not file_path.exists():
            return {}
        
        extension = file_path.suffix.lower()
        
        if extension not in self.parsers:
            return {}
        
        parser = self.parsers[extension]
        
        try:
            if hasattr(parser, 'get_metadata'):
                metadata = parser.get_metadata(file_path)
                metadata['file_extension'] = extension
                metadata['file_size'] = file_path.stat().st_size
                return metadata
            else:
                return {
                    'file_extension': extension,
                    'file_size': file_path.stat().st_size
                }
        except Exception as e:
            logger.error(f"Failed to extract metadata from {file_path}: {e}")
            return {}
    
    def is_supported(self, file_path: Path) -> bool:
        """
        Check if file format is supported.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if format is supported, False otherwise
        """
        if not isinstance(file_path, Path):
            file_path = Path(file_path)
        
        extension = file_path.suffix.lower()
        return extension in self.parsers
    
    def get_supported_extensions(self) -> list[str]:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported extensions
        """
        return list(self.parsers.keys())
