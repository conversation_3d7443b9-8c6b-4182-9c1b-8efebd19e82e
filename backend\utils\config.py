"""
Configuration management for TTS-WEB application.
"""

import os
from pathlib import Path
from typing import List, Optional
from dotenv import load_dotenv
from loguru import logger


class Config:
    """Application configuration management."""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        Initialize configuration.
        
        Args:
            env_file: Path to .env file (optional)
        """
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()
        
        self._validate_config()
    
    # Server settings
    @property
    def host(self) -> str:
        return os.getenv('HOST', '127.0.0.1')
    
    @property
    def port(self) -> int:
        return int(os.getenv('PORT', 8000))
    
    @property
    def debug(self) -> bool:
        return os.getenv('DEBUG', 'false').lower() == 'true'
    
    # File upload settings
    @property
    def max_file_size_mb(self) -> int:
        return int(os.getenv('MAX_FILE_SIZE_MB', 50))
    
    @property
    def allowed_extensions(self) -> List[str]:
        extensions = os.getenv('ALLOWED_EXTENSIONS', 'pdf,docx,txt,epub,rtf')
        return [f'.{ext.strip()}' for ext in extensions.split(',')]
    
    # TTS settings
    @property
    def tts_model(self) -> str:
        return os.getenv('TTS_MODEL', 'tts_models/ru/ruslan/tacotron2-DDC')
    
    @property
    def tts_language(self) -> str:
        return os.getenv('TTS_LANGUAGE', 'ru')
    
    @property
    def tts_speaker_wav(self) -> Optional[str]:
        speaker_wav = os.getenv('TTS_SPEAKER_WAV')
        return speaker_wav if speaker_wav else None
    
    @property
    def use_gpu(self) -> bool:
        return os.getenv('USE_GPU', 'false').lower() == 'true'
    
    # Audio settings
    @property
    def audio_format(self) -> str:
        return os.getenv('AUDIO_FORMAT', 'wav')
    
    @property
    def audio_sample_rate(self) -> int:
        return int(os.getenv('AUDIO_SAMPLE_RATE', 22050))
    
    @property
    def audio_bitrate(self) -> int:
        return int(os.getenv('AUDIO_BITRATE', 128))
    
    # Storage settings
    @property
    def upload_dir(self) -> Path:
        return Path(os.getenv('UPLOAD_DIR', 'uploads'))
    
    @property
    def output_dir(self) -> Path:
        return Path(os.getenv('OUTPUT_DIR', 'output'))
    
    @property
    def temp_dir(self) -> Path:
        return Path(os.getenv('TEMP_DIR', 'temp'))
    
    # Cleanup settings
    @property
    def auto_cleanup_hours(self) -> int:
        return int(os.getenv('AUTO_CLEANUP_HOURS', 24))
    
    @property
    def max_files_per_user(self) -> int:
        return int(os.getenv('MAX_FILES_PER_USER', 10))
    
    # Logging settings
    @property
    def log_level(self) -> str:
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @property
    def log_file(self) -> Optional[str]:
        return os.getenv('LOG_FILE')
    
    def _validate_config(self) -> None:
        """Validate configuration values."""
        try:
            # Validate numeric values
            if self.port < 1 or self.port > 65535:
                raise ValueError(f"Invalid port: {self.port}")
            
            if self.max_file_size_mb < 1:
                raise ValueError(f"Invalid max file size: {self.max_file_size_mb}")
            
            if self.audio_sample_rate < 8000:
                raise ValueError(f"Invalid sample rate: {self.audio_sample_rate}")
            
            # Create directories if they don't exist
            self.upload_dir.mkdir(parents=True, exist_ok=True)
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            
            # Create logs directory if log file is specified
            if self.log_file:
                log_path = Path(self.log_file)
                log_path.parent.mkdir(parents=True, exist_ok=True)
            
            logger.info("Configuration validated successfully")
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise
    
    def get_summary(self) -> dict:
        """
        Get configuration summary.
        
        Returns:
            Dictionary with configuration summary
        """
        return {
            'server': {
                'host': self.host,
                'port': self.port,
                'debug': self.debug
            },
            'files': {
                'max_size_mb': self.max_file_size_mb,
                'allowed_extensions': self.allowed_extensions,
                'upload_dir': str(self.upload_dir),
                'output_dir': str(self.output_dir)
            },
            'tts': {
                'model': self.tts_model,
                'language': self.tts_language,
                'use_gpu': self.use_gpu
            },
            'audio': {
                'format': self.audio_format,
                'sample_rate': self.audio_sample_rate,
                'bitrate': self.audio_bitrate
            }
        }
