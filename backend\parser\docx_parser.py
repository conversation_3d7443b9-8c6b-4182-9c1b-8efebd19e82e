"""
DOCX document parser using python-docx.
"""

from pathlib import Path
from typing import Optional
from docx import Document
from loguru import logger

from .base_parser import BaseParser


class DOCXParser(BaseParser):
    """Parser for DOCX documents using python-docx."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.docx', '.docm'}
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from DOCX file.
        
        Args:
            file_path: Path to the DOCX file
            
        Returns:
            Extracted text as string
            
        Raises:
            ValueError: If file format is not supported
            FileNotFoundError: If file doesn't exist
            Exception: If DOCX parsing fails
        """
        self.validate_file(file_path)
        
        try:
            logger.info(f"Extracting text from DOCX: {file_path}")
            
            doc = Document(str(file_path))
            text_parts = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_parts.append(' | '.join(row_text))
            
            if not text_parts:
                logger.warning(f"No text found in DOCX: {file_path}")
                return ""
            
            full_text = '\n'.join(text_parts)
            cleaned_text = self.clean_text(full_text)
            
            logger.info(f"Successfully extracted {len(cleaned_text)} characters from DOCX")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Failed to extract text from DOCX {file_path}: {e}")
            raise Exception(f"DOCX parsing failed: {e}") from e
    
    def get_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from DOCX file.
        
        Args:
            file_path: Path to the DOCX file
            
        Returns:
            Dictionary with metadata
        """
        try:
            doc = Document(str(file_path))
            core_props = doc.core_properties
            
            return {
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'keywords': core_props.keywords or '',
                'category': core_props.category or '',
                'comments': core_props.comments or '',
                'created': core_props.created.isoformat() if core_props.created else '',
                'modified': core_props.modified.isoformat() if core_props.modified else '',
                'paragraphs': len(doc.paragraphs),
                'tables': len(doc.tables)
            }
        except Exception as e:
            logger.error(f"Failed to extract metadata from DOCX {file_path}: {e}")
            return {}
