"""
File utilities for TTS-WEB application.
"""

import os
import uuid
import shutil
from pathlib import Path
from typing import Optional, List
from datetime import datetime, timedelta
from loguru import logger


class FileUtils:
    """Utility class for file operations."""
    
    @staticmethod
    def generate_unique_filename(original_filename: str, extension: Optional[str] = None) -> str:
        """
        Generate unique filename with timestamp and UUID.
        
        Args:
            original_filename: Original filename
            extension: File extension (optional)
            
        Returns:
            Unique filename
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        
        if extension:
            if not extension.startswith('.'):
                extension = f'.{extension}'
        else:
            extension = Path(original_filename).suffix
        
        base_name = Path(original_filename).stem
        safe_name = FileUtils.sanitize_filename(base_name)
        
        return f"{timestamp}_{unique_id}_{safe_name}{extension}"
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename by removing/replacing invalid characters.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Limit length
        if len(filename) > 100:
            filename = filename[:100]
        
        return filename or 'unnamed'
    
    @staticmethod
    def save_uploaded_file(file_content: bytes, filename: str, upload_dir: Path) -> Path:
        """
        Save uploaded file to disk.
        
        Args:
            file_content: File content as bytes
            filename: Original filename
            upload_dir: Upload directory
            
        Returns:
            Path to saved file
        """
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        unique_filename = FileUtils.generate_unique_filename(filename)
        file_path = upload_dir / unique_filename
        
        try:
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"File saved: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to save file {filename}: {e}")
            raise
    
    @staticmethod
    def validate_file_size(file_size: int, max_size_mb: int = 50) -> bool:
        """
        Validate file size.
        
        Args:
            file_size: File size in bytes
            max_size_mb: Maximum allowed size in MB
            
        Returns:
            True if size is valid, False otherwise
        """
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
    
    @staticmethod
    def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
        """
        Validate file extension.
        
        Args:
            filename: Filename to validate
            allowed_extensions: List of allowed extensions
            
        Returns:
            True if extension is valid, False otherwise
        """
        extension = Path(filename).suffix.lower()
        return extension in [ext.lower() for ext in allowed_extensions]
    
    @staticmethod
    def cleanup_old_files(directory: Path, max_age_hours: int = 24) -> int:
        """
        Clean up old files from directory.
        
        Args:
            directory: Directory to clean
            max_age_hours: Maximum age in hours
            
        Returns:
            Number of files deleted
        """
        if not directory.exists():
            return 0
        
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        deleted_count = 0
        
        try:
            for file_path in directory.iterdir():
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    
                    if file_time < cutoff_time:
                        file_path.unlink()
                        deleted_count += 1
                        logger.debug(f"Deleted old file: {file_path}")
            
            logger.info(f"Cleaned up {deleted_count} old files from {directory}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup files in {directory}: {e}")
            return 0
    
    @staticmethod
    def get_file_info(file_path: Path) -> dict:
        """
        Get file information.
        
        Args:
            file_path: Path to file
            
        Returns:
            Dictionary with file information
        """
        try:
            stat = file_path.stat()
            
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extension': file_path.suffix.lower()
            }
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return {}
